<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Podcast App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #FFFFFF;
            color: #1F1F1F;
            line-height: 1.5;
        }

        .container {
            max-width: 428px;
            margin: 0 auto;
            background: #FFFFFF;
            min-height: 100vh;
            position: relative;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 48px 32px 24px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
            background: #4C0099;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #4C0099;
        }

        .notification-btn {
            position: relative;
            width: 48px;
            height: 48px;
            background: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #FF5757;
            border-radius: 6px;
        }

        /* Search Bar */
        .search-container {
            padding: 0 32px;
            margin-bottom: 32px;
        }

        .search-bar {
            position: relative;
            background: rgba(31, 31, 31, 0.08);
            border-radius: 32px;
            padding: 20px 52px 20px 88px;
            border: none;
            width: 100%;
            font-size: 16px;
            color: #1F1F1F;
        }

        .search-bar::placeholder {
            color: rgba(31, 31, 31, 0.5);
        }

        .search-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
        }

        /* Carousel */
        .carousel-container {
            padding: 0 32px;
            margin-bottom: 32px;
        }

        .carousel {
            position: relative;
            height: 200px;
            border-radius: 24px;
            overflow: hidden;
            background: linear-gradient(135deg, #4C0099, #FF5757);
        }

        .carousel-slide {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            display: none;
        }

        .carousel-slide.active {
            display: block;
        }

        .carousel-dots {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .carousel-dot {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: background 0.3s;
        }

        .carousel-dot.active {
            background: white;
        }

        /* Section Headers */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 32px;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #1F1F1F;
        }

        .see-more {
            font-size: 16px;
            font-weight: 600;
            color: rgba(31, 31, 31, 0.7);
            text-decoration: none;
        }

        /* Podcast Cards */
        .podcast-grid {
            display: flex;
            gap: 16px;
            padding: 0 32px;
            margin-bottom: 40px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .podcast-grid::-webkit-scrollbar {
            display: none;
        }

        .podcast-card {
            flex: 0 0 auto;
            width: 364px;
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 16px;
        }

        .podcast-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            background: #f0f0f0;
            background-size: cover;
            background-position: center;
        }

        .podcast-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 700;
            color: #1F1F1F;
            margin-bottom: 8px;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            align-self: center;
        }

        .play-btn::before {
            content: '';
            width: 0;
            height: 0;
            border-left: 12px solid #4C0099;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            margin-left: 3px;
        }

        /* Continue Listening */
        .continue-section {
            margin-bottom: 40px;
        }

        .continue-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 0 32px;
            margin-bottom: 16px;
        }

        .continue-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            background: #f0f0f0;
            background-size: cover;
            background-position: center;
        }

        .continue-info {
            flex: 1;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #E9E9E9;
            border-radius: 4px;
            margin-top: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4C0099;
            border-radius: 4px;
        }

        /* Categories */
        .categories-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            padding: 0 32px;
            margin-bottom: 40px;
        }

        .category-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .category-image {
            width: 100%;
            height: 120px;
            border-radius: 16px;
            background: #f0f0f0;
            background-size: cover;
            background-position: center;
            margin-bottom: 16px;
        }

        .category-title {
            font-size: 16px;
            font-weight: 700;
            color: #1F1F1F;
            margin-bottom: 8px;
        }

        .category-count {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 428px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(32px);
            padding: 16px 32px;
            border-radius: 48px 48px 0 0;
        }

        .nav-container {
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            padding: 16px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            opacity: 0.5;
            transition: opacity 0.3s;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            background: #4C0099;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .nav-item.active .nav-icon {
            background: #4C0099;
        }

        .nav-item:not(.active) .nav-icon {
            background: transparent;
            color: #1F1F1F;
        }

        .nav-dot {
            width: 5px;
            height: 5px;
            background: #4C0099;
            border-radius: 50%;
            opacity: 0;
        }

        .nav-item.active .nav-dot {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">N</div>
                <div class="logo-text">Ncast</div>
            </div>
            <button class="notification-btn">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none">
                    <path d="M10.5 2C7.5 2 5 4.5 5 7.5V11L3 13V14H18V13L16 11V7.5C16 4.5 13.5 2 10.5 2Z" fill="#1F1F1F"/>
                    <path d="M8.5 16.5C8.5 17.6 9.4 18.5 10.5 18.5C11.6 18.5 12.5 17.6 12.5 16.5" fill="#1F1F1F"/>
                </svg>
                <div class="notification-badge"></div>
            </button>
        </header>

        <!-- Search Bar -->
        <div class="search-container">
            <div style="position: relative;">
                <svg class="search-icon" viewBox="0 0 24 24" fill="none">
                    <circle cx="11" cy="11" r="8" stroke="#1F1F1F" stroke-width="2"/>
                    <path d="m21 21-4.35-4.35" stroke="#1F1F1F" stroke-width="2"/>
                </svg>
                <input type="text" class="search-bar" placeholder="Search the podcast here...">
            </div>
        </div>

        <!-- Carousel -->
        <div class="carousel-container">
            <div class="carousel" id="carousel">
                <div class="carousel-slide active" style="background: linear-gradient(135deg, #FF6B6B, #4ECDC4);"></div>
                <div class="carousel-slide" style="background: linear-gradient(135deg, #A8E6CF, #FFD93D);"></div>
                <div class="carousel-slide" style="background: linear-gradient(135deg, #FF8A80, #81C784);"></div>
                <div class="carousel-dots">
                    <div class="carousel-dot active" onclick="showSlide(0)"></div>
                    <div class="carousel-dot" onclick="showSlide(1)"></div>
                    <div class="carousel-dot" onclick="showSlide(2)"></div>
                </div>
            </div>
        </div>

        <!-- Promoted Podcasts -->
        <div class="section-header">
            <h2 class="section-title">Promoted Podcasts</h2>
        </div>
        <div class="podcast-grid">
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">See Mama Be</div>
                    <div class="podcast-category">Creative Studio</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #f093fb, #f5576c);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">Your Time</div>
                    <div class="podcast-category">Educational</div>
                    <div class="podcast-duration">25 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
        </div>

        <!-- Trending Podcasts -->
        <div class="section-header">
            <h2 class="section-title">Trending Podcasts</h2>
            <a href="#" class="see-more">See more</a>
        </div>
        <div class="podcast-grid">
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #a8edea, #fed6e3);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">Festival on the Beach</div>
                    <div class="podcast-category">Rock Electrics</div>
                    <div class="podcast-duration">10 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #ffecd2, #fcb69f);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">Music Theme</div>
                    <div class="podcast-category">Lo-fi Music</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
        </div>

        <!-- Continue Listening -->
        <div class="section-header">
            <h2 class="section-title">Continue Listening</h2>
            <a href="#" class="see-more">See more</a>
        </div>
        <div class="continue-section">
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Talk Show - Ep7</div>
                    <div class="podcast-category">15 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #a18cd1, #fbc2eb);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Musical Soul - Vol. 3</div>
                    <div class="podcast-category">35 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 30%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #fad0c4, #ffd1ff);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Let's Stand Up</div>
                    <div class="podcast-category">5 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #ffecd2, #fcb69f);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Talk Show - Ep10</div>
                    <div class="podcast-category">30 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 45%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
        </div>

        <!-- Top Categories -->
        <div class="section-header">
            <h2 class="section-title">Top Categories</h2>
            <a href="#" class="see-more">See more</a>
        </div>
        <div class="categories-grid">
            <div class="category-card">
                <div class="category-image" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                <div class="category-title">Business</div>
                <div class="category-count">125 podcasts</div>
            </div>
            <div class="category-card">
                <div class="category-image" style="background: linear-gradient(135deg, #f093fb, #f5576c);"></div>
                <div class="category-title">Healthy Lifestyle</div>
                <div class="category-count">158 podcasts</div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-container">
                <div class="nav-item">
                    <div class="nav-icon">🧭</div>
                    <div class="nav-dot"></div>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">🎧</div>
                    <div class="nav-dot"></div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">❤️</div>
                    <div class="nav-dot"></div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">👤</div>
                    <div class="nav-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const dots = document.querySelectorAll('.carousel-dot');

        function showSlide(index) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('active');
            
            currentSlide = index;
            
            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        // Auto-play carousel
        setInterval(() => {
            const nextSlide = (currentSlide + 1) % slides.length;
            showSlide(nextSlide);
        }, 3000);

        // Navigation
        document.querySelectorAll('.nav-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                document.querySelector('.nav-item.active').classList.remove('active');
                item.classList.add('active');
            });
        });
    </script>
</body>
</html>
